/* GamePort - Default Theme (米色系) */
/* 游戏站模板的原始配色方案，温暖米色调 */

:root {
  /* 主要颜色变量 - 米色系 */
  --primary-color: #FDFBFA;
  --primary-color-2: #FFFFFF;
  --primary-hover: #F5F1EC;
  --primary-light: #FDFBFA;
  --secondary-color: #8B7355;
  --accent-color: #D4AF37;
  
  /* 背景颜色 */
  --bg-primary: #F0ECE8;
  --bg-secondary: #FFFFFF;
  --bg-tertiary: #DBD7D2;
  --bg-dark: #1F1C1A;
  
  /* Header专用背景色 - 比主背景更深 */
  --header-bg-color: #1F1C1A;
  
  /* 文字颜色 */
  --text-primary: #333333;
  --text-secondary: #333333;
  --text-light: #666666;
  --text-white: #FDFBFA;
  
  /* 辅助颜色变量 */
  --border-color: #333333;
  --border-hover: #555555;
  --shadow-color: rgba(31, 28, 26, 0.1);
  --shadow-hover: rgba(31, 28, 26, 0.15);
  
  /* 状态颜色 */
  --success-color: #7C9885;
  --warning-color: #D4AF37;
  --error-color: #B85450;
  --info-color: #8B7355;
  
  /* 按钮颜色状态 */
  --btn-primary-bg: #1F1C1A;
  --btn-primary-hover: #2D2A27;
  --btn-primary-active: #141210;
  --btn-secondary-bg: #8B7355;
  --btn-secondary-hover: #6B5A44;
  
  /* 链接颜色 */
  --link-color: #8B7355;
  --link-hover: #6B5A44;
  --link-visited: #A0845C;
  
  /* 特殊效果颜色 */
  --gradient-primary: linear-gradient(135deg, #1F1C1A 0%, #2D2A27 100%);
  --gradient-secondary: linear-gradient(135deg, #F0ECE8 0%, #DBD7D2 100%);
}

/* 搜索框样式覆盖 */
.search-box {
  border: none !important;
}
