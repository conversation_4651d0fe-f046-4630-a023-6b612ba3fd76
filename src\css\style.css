/* GamePort - 主样式文件 */
/* 使用CSS变量体系支持多主题切换 */

/* ===== CSS变量定义 ===== */
:root {
  /* 主要颜色变量 */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --primary-light: #dbeafe;
  --secondary-color: #64748b;
  --accent-color: #f59e0b;
  
  /* 背景颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #e2e8f0;
  --bg-dark: #1e293b;
  
  /* 文字颜色 */
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-light: #94a3b8;
  --text-white: #ffffff;
  
  /* 辅助颜色变量 */
  --border-color: #e2e8f0;
  --border-hover: #cbd5e1;
  --shadow-color: rgba(0, 0, 0, 0.1);
  --shadow-hover: rgba(0, 0, 0, 0.15);
  
  /* 状态颜色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
  
  /* 字体变量 */
  --font-family-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-heading: 'Arial', 'Helvetica', sans-serif;
  --font-family-mono: 'Courier New', monospace;
  
  /* 字体大小 */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* 字重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* 间距变量 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* 圆角 */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  --border-radius-2xl: 1rem;
  
  /* 动画变量 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 var(--shadow-color);
  --shadow-md: 0 4px 6px -1px var(--shadow-color);
  --shadow-lg: 0 10px 15px -3px var(--shadow-color);
  --shadow-xl: 0 20px 25px -5px var(--shadow-color);
  
  /* 布局变量 */
  --container-max-width: 1200px;
  --container-list-max-width: 1400px; /* 分类页面专用宽度 */
  --sidebar-width: 300px;
  --header-height: 80px;
  --footer-height: 60px;

  /* 动态主内容区宽度变量 */
  --main-content-width-small: 854px;
  --main-content-width-large: 960px;
  --sidebar-width-1col: 250px;
  --sidebar-width-2col: 370px;
}

/* ===== 基础重置样式 ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== 链接样式 ===== */
a {
  /* color: var(--primary-color); */
  color: var(--primary-color-2);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

a:visited {
  /* color: var(--secondary-color); */
}

/* ===== 标题样式 ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

/* ===== 段落和文本 ===== */
p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

/* ===== 按钮基础样式 ===== */
.btn {
  display: inline-block;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-align: center;
  border: none;
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* ===== 卡片样式 ===== */
.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.card:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* ===== 容器和布局 ===== */
.container {
  width: fit-content; /* 宽度适应内容，同时支持居中 */
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  /* 页面宽度由content-grid自动决定，通过fit-content实现居中 */
}

/* 分类页面专用容器 */
.list-page .container {
  max-width: var(--container-list-max-width);
}

.flex {
  display: flex;
}

.grid {
  display: grid;
}

.hidden {
  display: none;
}

/* ===== Header样式 ===== */
.site-header {
  background-color: var(--header-bg-color);
  /* border-bottom: 1px solid var(--border-color); */
  box-shadow: var(--shadow-sm);
  position: relative;
  width: 100%; /* header占满整个屏幕宽度 */
}

.main-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 80px; /* 上下边距保持，左右设置为50px */
  min-height: var(--header-height);
  width: 100%; /* nav占满header宽度 */
  box-sizing: border-box; /* 确保padding不会增加总宽度 */
}

.nav-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text-primary);
}

.brand-logo {
  width: 40px;
  height: 40px;
  margin-right: var(--spacing-sm);
}

.brand-text {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.nav-menu {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--spacing-lg);
}

.nav-link {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  font-weight: var(--font-weight-medium);
}

.nav-link:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.nav-link.active {
  color: var(--primary-color);
}

.nav-search {
  display: flex;
  align-items: center;
}

.search-box {
  display: flex;
  align-items: center;
  /* background-color: var(--bg-secondary); */
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xs);
}

.search-input {
  border: none;
  background: transparent;
  padding: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  width: 200px;
}

.search-input:focus {
  outline: none;
}

.search-btn {
  background: none;
  border: none;
  padding: var(--spacing-sm);
  cursor: pointer;
  color: var(--text-light);
}

.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-sm);
}

.hamburger-line {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  margin: 2px 0;
  transition: var(--transition-fast);
}

/* ===== Breadcrumb样式 ===== */
.breadcrumb-nav {
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-color);
}

.breadcrumb {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.breadcrumb-link {
  color: var(--primary-color);
  text-decoration: none;
}

.breadcrumb-separator {
  margin: 0 var(--spacing-sm);
  color: var(--text-light);
}

.breadcrumb-current {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* ===== Main Content样式 ===== */
.main-content {
  padding: var(--spacing-xl) 0 0; /* 只保留上内边距，删除下内边距 */
  min-height: calc(100vh - var(--header-height) - var(--footer-height));
}

.content-grid {
  display: grid;
  gap: var(--spacing-xl);
  align-items: start;
}

/* iframe尺寸 + Popular Games列数 组合控制布局 */
.iframe-small.popular-1col .content-grid {
  grid-template-columns: var(--main-content-width-small) var(--sidebar-width-1col);
}

.iframe-small.popular-2col .content-grid {
  grid-template-columns: var(--main-content-width-small) var(--sidebar-width-2col);
}

.iframe-large.popular-1col .content-grid {
  grid-template-columns: var(--main-content-width-large) var(--sidebar-width-1col);
}

.iframe-large.popular-2col .content-grid {
  grid-template-columns: var(--main-content-width-large) var(--sidebar-width-2col);
}

.content-primary {
  display: flex;
  flex-direction: column;
  /* 删除gap，改为每个区域单独用margin控制间距 */
}

.content-sidebar {
  position: relative;
}

/* ===== Game Section样式 ===== */
.game-section {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  margin-bottom: 30px; /* 与下一个区域的间距 */
}

.game-container {
  position: relative;
  width: 100%;
  /* border: 1px solid #e9ecef; */
  border-radius: 8px 8px 0 0;
  background: #fff;
  overflow: hidden;
}





.game-frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* ===== 游戏预览界面样式 ===== */
.game-preview {
  width: 100%;
  height: 450px; /* 默认高度 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  padding: 20px;
  gap: 20px;
}



.game-image {
  max-width: 300px;
  height: auto;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  object-fit: contain;
}

.play-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 15px 40px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
  transition: all 0.3s ease;
}

.play-btn:hover {
  background: #0056CC;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 122, 255, 0.4);
}

.play-icon {
  font-size: 16px;
}

/* ===== Game Loading State样式 ===== */
.game-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid var(--border-color);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.game-loading p {
  color: var(--text-secondary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

/* ===== 响应式设计 - Game Preview ===== */
@media (max-width: 768px) {
  .play-now-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: var(--font-size-base);
  }
  
  .play-icon {
    font-size: var(--font-size-lg);
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
  }
  
  .game-loading p {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  .play-now-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-sm);
  }
  
  .game-preview:hover {
    transform: none; /* 移动端禁用hover缩放效果 */
  }
}

/* ===== Games Section样式 ===== */
.games-section {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

/* New Games Section 特殊样式 - 去掉边框和背景 */
.new-games-section {
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  margin-bottom: 30px; /* 与下一个区域的间距，确保Game Feature上边框可见 */
}

/* Popular Games Section 特殊样式 - 去掉边框和背景 */
.popular-games-section {
  background-color: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
}

/* Popular - mobile placement (show here, hide sidebar version) */
.popular-mobile { display: none; }

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  padding-bottom: 0;
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.section-link {
  font-size: var(--font-size-sm);
  /* color: var(--primary-color); */
  color: var(--text-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.section-link:hover {
  color: var(--primary-hover);
}

/* ===== Games Grid样式 ===== */
.games-grid {
  display: grid;
  gap: var(--spacing-lg);
}

/* 主内容区域的游戏网格 (New Games) - 2行4列布局 */
.games-grid-main {
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, auto);
  /* 删除max-height限制，让容器高度由内容自动撑开 */
  gap: var(--spacing-md); /* 缩短卡片间距 */
}

/* 侧边栏的游戏网格 (Popular Games) - 基础样式 */
.games-grid-sidebar {
  display: grid;
  gap: var(--spacing-md);
  /* 列数由动态类控制，不在这里设置 grid-template-columns */
}

/* 默认情况下使用1列布局（后备样式） */
body:not(.popular-1col):not(.popular-2col) .games-grid-sidebar {
  grid-template-columns: 1fr;
  gap: var(--spacing-sm);
}

body:not(.popular-1col):not(.popular-2col) .games-grid-sidebar .game-thumbnail {
  height: 110px;
  width: 100%;
  max-width: 250px;
}

/* Popular Games 动态列数样式 */
/* 1列模式 - 默认 */

.popular-1col .content-sidebar {
  width: 250px;
}

.popular-1col .games-grid-sidebar {
  grid-template-columns: 1fr;
  gap: var(--spacing-sm);
}

.popular-1col .games-grid-sidebar .game-thumbnail {
  height: 110px;
  width: 100%;
  max-width: 250px;
}

/* 2列模式 */

.popular-2col .content-sidebar {
  width: 370px;
}

.popular-2col .games-grid-sidebar {
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm);
}

.popular-2col .games-grid-sidebar .game-thumbnail {
  height: 101px;
  width: 100%;
  max-width: 180px;
}

/* ===== iframe 动态尺寸样式 ===== */
/* 页面宽度由content-grid自动决定，无需手动计算container宽度 */

/* iframe 尺寸优先级覆盖 */
.iframe-small .game-iframe,
.iframe-large .game-iframe {
  border: none;
  display: none; /* 默认隐藏，等待startGame()显示 */
}

/* 列表页面的游戏网格 */
.games-grid-list {
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: var(--spacing-lg);
}

.game-card {
  background-color: var(--bg-primary);
  /* border: 1px solid var(--border-color); */
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all var(--transition-normal);
  text-decoration: none;
  color: inherit;
  position: relative;
  box-shadow: var(--shadow-sm);
  display: block;
}

.game-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.game-thumbnail {
  width: 100%;
  height: 120px;
  object-fit: cover;
  /* background-color: var(--bg-tertiary); */
  display: block;
}

/* 侧边栏游戏卡片的缩略图 */
.games-grid-sidebar .game-thumbnail {
  height: 90px;
}

/* 主内容区游戏卡片的缩略图 */
.games-grid-main .game-thumbnail {
  height: 135px; /* 设置为135px */
}

/* New Games 区域游戏卡片特殊样式 */
.new-games-section .game-card {
  min-width: 0; /* 确保卡片能够缩放 */
  width: 100%; /* 占满可用宽度 */
}

/* 列表页游戏卡片的缩略图 */
.games-grid-list .game-thumbnail {
  height: 140px;
  object-fit: cover;
}

.game-info {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--bg-tertiary);
}

.game-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-tight);
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 主内容区游戏标题 */
.games-grid-main .game-title {
  font-size: var(--font-size-base);
}

/* 列表页游戏标题 */
.games-grid-list .game-title {
  font-size: var(--font-size-base);
}

/* 游戏卡片悬停时的标题效果 */
.game-card:hover .game-title {
  color: var(--primary-color);
}

/* ===== Game Detail页面样式 ===== */
.game-header {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl) 0;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
}

.game-header-content {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: var(--spacing-xl);
  align-items: center;
}

.game-thumbnail-large {
  width: 200px;
  height: 150px;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.game-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.game-header-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.game-title-large {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--line-height-tight);
}

.game-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

/* Game Features Section - 删除无用样式 */

.feature-block {
  margin: 0;
  padding: 0;
  background: none;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.feature-block h2 {
  color: var(--primary-color);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-lg) 0;
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.feature-block p {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-lg) 0;
}

/* 列表格式样式 */
.feature-block ol,
.feature-block ul {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--spacing-lg) 0;
  padding-left: var(--spacing-xl);
}

.feature-block li {
  margin-bottom: var(--spacing-sm);
  padding-left: var(--spacing-xs);
}

.feature-block li:last-child {
  margin-bottom: 0;
}

/* YouTube Video Embed */
.youtube-embed {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  margin: 0 0 var(--spacing-lg) 0;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.youtube-embed iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}

/* Related Games Section */
.related-games {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 2px solid var(--border-color);
}

/* ===== 列表页面样式 ===== */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  /* padding: var(--spacing-xl) 0; */
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
  border-radius: var(--border-radius-lg);
  /* border: 1px solid var(--border-color); */
}

.page-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  /* color: var(--primary-color); */
  /* margin: 0 0 var(--spacing-md) 0; */
  color: var(--text-primary);
  margin: 0 0 0 0;
  padding: 10px 0 0 0 ;
  line-height: var(--line-height-tight);
}

.page-subtitle{
  padding: 0 0 10px 0;
}

.page-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: var(--line-height-relaxed);
}

/* ===== 分类列表页面样式 ===== */
.games-list-section {
  margin-bottom: var(--spacing-xl);
}

/* 分类列表页面容器 */
.games-list-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 分类列表页面的游戏网格 - 6列固定布局 */
.games-grid-list {
  display: grid;
  grid-template-columns: repeat(6, 209px);
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
  padding: 0;
  margin-top: var(--spacing-lg);
}

/* 分类页面游戏卡片固定尺寸 */
.games-grid-list .game-card {
  width: 209px;
}

.games-grid-list .game-thumbnail {
  width: 209px;
  height: 118px;
  object-fit: cover;
  border-radius: var(--border-radius-md);
}

/* ===== 分页导航样式 ===== */
.pagination-nav {
  margin: 30px 0;
  padding: 20px 0;
  border-top: 1px solid var(--border-color);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.pagination-btn {
  padding: 8px 16px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  text-decoration: none;
  border-radius: var(--border-radius-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  border: 1px solid var(--border-color);
}

.pagination-btn:hover {
  background: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.pagination-number {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-md);
  background: var(--bg-secondary);
  color: var(--text-primary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  border: 1px solid var(--border-color);
}

.pagination-number:hover {
  background: var(--primary-light);
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.pagination-number.active {
  background: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.pagination-dots {
  color: var(--text-light);
  font-weight: var(--font-weight-bold);
  padding: 0 8px;
}

/* ===== Game Feature Section样式 ===== */
.game-feature-section {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  /* border-top: 1px solid var(--border-color); 添加上边框线 */
  margin-bottom: 30px; /* 与下一个区域的间距 */
}

.feature-content h2,
.feature-content h3,
.feature-content h4 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.feature-content p {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
}

.feature-content ul {
  color: var(--text-secondary);
  padding-left: var(--spacing-lg);
}

.feature-content li {
  margin-bottom: var(--spacing-sm);
}

/* ===== FAQ Section样式 ===== */
.faq-section {
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  /* border-top: 1px solid var(--border-color); 添加上边框线 */
  /* 删除margin-bottom，避免与Footer的margin-top重复 */
}

.faq-item {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.faq-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.faq-question {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.faq-answer {
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* 重复的page-header样式已删除，使用上面的定义 */

/* ===== Games Grid Section样式 (用于列表页) ===== */
.games-grid-section {
  margin-bottom: var(--spacing-2xl);
}

.games-grid-container {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
}

.load-more-section {
  text-align: center;
  margin-top: var(--spacing-xl);
}

.load-more-btn {
  padding: var(--spacing-md) var(--spacing-2xl);
  font-size: var(--font-size-lg);
}

/* ===== Footer样式 ===== */
.site-footer {
  background-color: var(--bg-dark);
  color: var(--text-white);
  margin-top: 30px; /* 与上一个区域保持30px间距 */
  width: 100%; /* footer占满整个屏幕宽度 */
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl) 150px; /* 左右边距50px，上下保持原有间距 */
  box-sizing: border-box; /* 确保padding不会增加总宽度 */
}

.footer-info p {
  margin: 0;
  color: var(--text-white);
  opacity: 0.8;
}

.footer-links {
  display: flex;
  gap: var(--spacing-lg);
}

.footer-link {
  color: var(--text-white);
  text-decoration: none;
  opacity: 0.8;
  transition: opacity var(--transition-fast);
}

.footer-link:hover {
  opacity: 1;
  color: var(--primary-color);
}

/* ===== 响应式设计 ===== */
@media (max-width: 1024px) {
  /* Container should span device width and stay centered */
  .container {
    width: 100%;
    max-width: 100%;
    padding: 0 var(--spacing-lg);
    box-sizing: border-box;
  }
  .content-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  /* Shrink header horizontal padding to avoid overflow */
  .main-nav {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  /* Tablet: Popular (mobile section) uses same grid as New Games */
  .popular-mobile .games-grid-sidebar {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-sm);
  }
  .popular-mobile .games-grid-sidebar .game-thumbnail { height: 135px; }

  .content-sidebar {
    position: static;
    order: 99; /* push sidebar to the end on tablet/mobile */
  }

  .games-grid-main {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, auto);
    /* 删除max-height限制 */
    gap: var(--spacing-sm);
  }

  /* 平板端基础样式 - 由动态类控制 */
  body:not(.popular-1col):not(.popular-2col) .games-grid-sidebar {
    grid-template-columns: 1fr;
  }

  /* 平板端强制使用1列布局 */
  .popular-2col .games-grid-sidebar {
    grid-template-columns: 1fr;
  }

  .popular-2col .games-grid-sidebar .game-thumbnail {
    height: 110px;
    max-width: 250px;
  }

  /* 分类页面响应式 - 平板端改为5列 */
  .games-grid-list {
    grid-template-columns: repeat(5, 1fr);
    gap: var(--spacing-md);
  }

  .games-grid-list .game-card,
  .games-grid-list .game-thumbnail {
    width: 100%;
  }

  .games-grid-list .game-thumbnail {
    height: 140px;
  }

  /* iframe 响应式 - 平板端 */
  /* container宽度由content-grid自动决定，无需手动设置 */

  .iframe-small .game-iframe,
  .iframe-large .game-iframe {
    width: 100%; /* 平板端iframe占满容器宽度 */
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .container { padding: 0 var(--spacing-md); width: 100%; max-width: 100%; }

  .main-nav { flex-wrap: wrap; gap: var(--spacing-md); padding: var(--spacing-md); }

  .nav-menu {
    display: none;
    width: 100%;
    order: 3;
  }

  .nav-menu.active {
    display: block;
  }

  .nav-list {
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) 0;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  /* Reorder sections on mobile: place Popular after iframe */
  .popular-mobile { display: block; }
  .content-sidebar { display: none; }

  /* Phone: Popular (mobile section) uses same grid as New Games */
  .popular-mobile .games-grid-sidebar {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }
  .popular-mobile .games-grid-sidebar .game-thumbnail { height: 135px; }

  .nav-search {
    order: 2;
  }

  .search-input {
    width: 150px;
  }

  .games-grid-main {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(4, auto);
    /* 删除max-height限制 */
    gap: var(--spacing-sm);
  }

  /* 分类页面响应式 - 手机端改为4列 */
  .games-grid-list {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-sm);
  }

  .games-grid-list .game-thumbnail {
    height: 120px;
  }

  /* 小屏幕基础样式 - 由动态类控制 */
  body:not(.popular-1col):not(.popular-2col) .games-grid-sidebar {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  /* 小屏幕强制使用1列布局 */
  .popular-2col .games-grid-sidebar {
    grid-template-columns: 1fr;
  }

  .popular-2col .games-grid-sidebar .game-thumbnail {
    height: 110px;
    max-width: 200px;
  }

  .page-title {
    font-size: var(--font-size-3xl);
  }

  /* Game Detail响应式 */
  .game-header-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-lg);
  }

  .game-thumbnail-large {
    width: 150px;
    height: 112px;
    margin: 0 auto;
  }

  .game-title-large {
    font-size: var(--font-size-2xl);
  }

  .game-actions {
    justify-content: center;
  }

  .feature-block {
    padding: var(--spacing-lg);
  }

  .feature-block h2 {
    font-size: var(--font-size-xl);
  }

  .footer-content { flex-direction: column; text-align: center; padding: var(--spacing-lg) var(--spacing-md); }

  h1 { font-size: var(--font-size-3xl); }
  h2 { font-size: var(--font-size-2xl); }
  h3 { font-size: var(--font-size-xl); }

  /* iframe 响应式 - 移动端 */
  .iframe-small .game-iframe,
  .iframe-large .game-iframe {
    width: 100%; /* 移动端iframe占满容器宽度 */
    height: 300px; /* 移动端降低高度 */
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .container { padding: 0 var(--spacing-md); width: 100%; max-width: 100%; }
  .main-nav { padding: var(--spacing-md); }
  .footer-content { padding: var(--spacing-lg) var(--spacing-md); }
  .games-grid-main {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(4, auto);
    /* 删除max-height限制 */
    gap: var(--spacing-sm);
  }

  /* 分类页面响应式 - 小屏幕改为3列 */
  .games-grid-list {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-xs);
  }

  .games-grid-list .game-thumbnail {
    height: 100px;
  }

  /* 分页导航响应式 */
  .pagination {
    gap: 4px;
  }

  .pagination-number {
    width: 36px;
    height: 36px;
    font-size: var(--font-size-xs);
  }

  .pagination-btn {
    padding: 6px 12px;
    font-size: var(--font-size-xs);
  }

  /* 最小屏幕基础样式 - 由动态类控制 */
  body:not(.popular-1col):not(.popular-2col) .games-grid-sidebar {
    grid-template-columns: 1fr;
  }

  /* 最小屏幕强制使用1列布局 */
  .popular-2col .games-grid-sidebar {
    grid-template-columns: 1fr;
  }

  .popular-2col .games-grid-sidebar .game-thumbnail {
    height: 100px;
    max-width: 180px;
  }

  .brand-text {
    display: none;
  }

  .nav-search {
    display: none;
  }

  .page-header {
    padding: var(--spacing-lg) var(--spacing-md);
  }
}



/* 活跃导航链接 */
.nav-link.active {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  background-color: transparent;
}
/* ===== Game Error State样式 ===== */
.game-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xl);
}

.error-icon {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
}

.error-message {
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  margin-bottom: var(--spacing-lg);
  max-width: 300px;
}

.retry-btn {
  background: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.retry-btn:hover {
  background: var(--primary-hover);
}

.retry-btn:active {
  transform: translateY(1px);
}

/* ===== 游戏iframe样式 ===== */

.game-iframe {
    width: 100%;
    height: 450px;
    margin: 0;
    display: none;
}

.game-info-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
    /* border: 1px solid #e9ecef; */
    border-top: none;
    border-radius: 0 0 8px 8px;
    padding: 8px 12px;
    margin: 0;
    height: 56px;
    box-sizing: border-box;
}

.game-info-left {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.game-info-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
    border: 1px solid #dee2e6;
}

.game-info-center {
    flex: 1;
    margin: 0 12px;
    overflow: hidden;
}

.game-info-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    margin: 0;
}

.game-info-right {
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.fullscreen-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 6px;
    background: #007bff;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    font-size: 16px;
}

.fullscreen-btn:hover {
    background: #0056b3;
    transform: scale(1.05);
}

.fullscreen-btn:active {
    transform: scale(0.95);
}

.fullscreen-icon {
    display: block;
    line-height: 1;
}

/* 响应式适配 */
@media (max-width: 768px) {
    .game-info-bar {
        padding: 6px 8px;
        height: 52px;
    }
    
    .game-info-thumbnail {
        width: 36px;
        height: 36px;
    }
    
    .fullscreen-btn {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
    
    .game-info-title {
        font-size: 14px;
    }
    
    .game-info-center {
        margin: 0 8px;
    }
}
/* ===== 
搜索功能样式 ===== */
.nav-search {
    position: relative;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    padding: 8px 40px 8px 12px;
    /* border: 1px solid var(--border-color); */
    border-radius: 20px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 14px;
    width: 200px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    width: 250px;
}

.search-btn {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 6px;
    cursor: pointer;
    border-radius: 50%;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.search-btn:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
}

/* 搜索结果下拉框 */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    /* border: 1px solid var(--border-color); */
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    margin-top: 4px;
    display: none;
}

.search-results.show {
    display: block;
}

.search-result-item {
    display: flex;
    align-items: center;
    padding: 12px;
    cursor: pointer;
    /* border-bottom: 1px solid var(--border-color); */
    transition: background-color 0.2s ease;
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-item:hover,
.search-result-item.selected {
    background: var(--bg-secondary);
}

.search-result-item:active {
    background: var(--primary-color);
    color: white;
    transform: scale(0.98);
}

.search-result-thumbnail {
    width: 40px;
    height: 40px;
    border-radius: 6px;
    object-fit: cover;
    margin-right: 12px;
    border: 1px solid var(--border-color);
}

.search-result-info {
    flex: 1;
}

.search-result-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 2px;
}

.search-result-category {
    font-size: 12px;
    color: var(--text-secondary);
    text-transform: capitalize;
}

.search-no-results {
    padding: 20px;
    text-align: center;
    color: var(--text-secondary);
}

.search-loading {
    padding: 20px;
    text-align: center;
    color: var(--text-secondary);
}

/* 搜索关键词高亮 */
.search-highlight {
    background: rgba(255, 235, 59, 0.3);
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-input {
        width: 150px;
    }
    
    .search-input:focus {
        width: 180px;
    }
    
    .search-results {
        left: -50px;
        right: -50px;
    }
}

@media (max-width: 480px) {
    .search-input {
        width: 120px;
    }

    .search-input:focus {
        width: 140px;
    }
}

/* ===== 法律页面样式 ===== */
.legal-page {
    background: var(--bg-secondary);
    min-height: 100vh;
}

.legal-content {
    width: 100%;
    padding: 40px 0;
}

.legal-header {
    text-align: center;
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--border-color);
}

.legal-header h1 {
    font-size: var(--font-size-4xl);
    color: var(--text-primary);
    margin-bottom: 16px;
    font-weight: 700;
}

.legal-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 800px;
    margin: 0 auto;
}

.legal-document {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 4px 6px var(--shadow-color);
    border: 1px solid var(--border-color);
}

.legal-section {
    margin-bottom: 32px;
}

.legal-section:last-child {
    margin-bottom: 0;
}

.legal-section h2 {
    font-size: var(--font-size-2xl);
    color: var(--text-primary);
    margin-bottom: 16px;
    font-weight: 600;
    border-left: 4px solid var(--primary-color);
    padding-left: 16px;
}

.legal-section p {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    line-height: 1.7;
    margin-bottom: 16px;
}

.legal-section p:last-child {
    margin-bottom: 0;
}

.legal-section a {
    color: var(--primary-color);
    text-decoration: underline;
    transition: color 0.2s ease;
}

.legal-section a:hover {
    color: var(--primary-hover);
}

/* 邮箱地址特殊样式 */
.legal-section a[href^="mailto:"] {
    font-size: var(--font-size-lg);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-decoration: none;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 2px;
    transition: all 0.3s ease;
}

.legal-section a[href^="mailto:"]:hover {
    transform: scale(1.05);
    border-bottom-color: var(--accent-color);
}

/* 法律页面列表样式优化 */
.legal-section ul {
    margin: 16px 0;
    padding-left: 0;
    list-style: none;
}

.legal-section li {
    position: relative;
    padding-left: 24px;
    margin-bottom: 8px;
    line-height: 1.6;
}

.legal-section li::before {
    content: "•";
    position: absolute;
    left: 0;
    top: 0;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.2em;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .legal-content {
        padding: 20px 16px;
    }

    .legal-header h1 {
        font-size: var(--font-size-3xl);
    }

    .legal-subtitle {
        font-size: var(--font-size-base);
    }

    .legal-document {
        padding: 24px 20px;
    }

    .legal-section h2 {
        font-size: var(--font-size-xl);
    }
}

@media (max-width: 480px) {
    .legal-content {
        padding: 16px 12px;
    }

    .legal-header {
        margin-bottom: 24px;
    }

    .legal-header h1 {
        font-size: var(--font-size-2xl);
    }

    .legal-document {
        padding: 20px 16px;
    }

    .legal-section {
        margin-bottom: 24px;
    }

    .legal-section h2 {
        font-size: var(--font-size-lg);
        padding-left: 12px;
    }
}