/* GamePort - Abyssal Glow Theme (深渊幽光) - v2 优化版 */
/* 严格遵循用户指定的核心颜色，并以此为基础进行补充和协调。此版本对补充色进行二次优化。*/

:root {
  /* =================================================== */
  /* == 用户指定的核心颜色 (保证绝对不会修改) == */
  /* =================================================== */
  --header-bg-color: #00111A;
  --bg-primary: #001B29;
  --bg-secondary: #00334D;
  --border-color: #00C4C5;
  --bg-dark: #00111A;
  --primary-color: #00D4FF;
  --primary-color-2: #00D4FF;
  --text-primary: #00D4FF;
  --text-secondary: #00C4C5;
  /* (注：原CSS无此变量，按您的要求添加) */
  --text-tertiary: #00D4FF;
  /* =================================================== */


  /* =================================================== */
  /* == 基于核心色补充与协调的颜色 (内容优化) == */
  /* =================================================== */

  /* 交互与强调色 */
  --primary-hover: #00bfe6;      /* 主色悬停 - 改为稍暗的青色，交互感更沉稳 */
  --primary-light: #d0f8ff;      /* 轻柔的亮色 - 改为更亮的“幽灵白”，用于细微高光 */
  --secondary-color: #00C4C5;    /* 次要功能色 - (维持) 沿用您的次要文本色，保持统一 */
  --accent-color: #00D4FF;       /* 强调色 - (维持) 沿用您的主色 */

  /* 背景色补充 */
  --bg-tertiary: #004564;       /* 第三层背景，用于悬停或特殊区域 - 调整为更微妙的深蓝色 */

  /* 文字颜色补充 */
  --text-light: #a0b3c2;         /* 辅助性/说明性文字 - 改为更中性的蓝灰色，可读性更佳 */
  --text-white: #faffff;         /* 白色 - 改为接近纯白但带有极微弱青色的白，对比更强 */

  /* 辅助与装饰 */
  --border-hover: #33E1FF;       /* 边框悬停 - 调整为比主色更亮的青色，发光感更强 */
  --shadow-color: rgba(0, 212, 255, 0.2);  /* 辉光效果 - 辉光稍微增强 */
  --shadow-hover: rgba(0, 212, 255, 0.3);   /* 悬停辉光 - 同样增强，拉开差距 */

  /* 状态颜色 (维持高亮版) */
  --success-color: #10B981;
  --warning-color: #FBBF24;
  --error-color: #F43F5E;
  --info-color: #00D4FF;

  /* 按钮颜色状态 (提供不同的按钮风格) */
  --btn-primary-bg: #005F73;     /* 主要按钮背景 - 改为更深的暗青色，更稳重 */
  --btn-primary-hover: #007a94;   /* 主要按钮悬停 - 背景色变亮 */
  --btn-primary-active: #004a5c;  /* 主要按钮激活 - 背景色加深 */
  --btn-secondary-bg: transparent; /* 次要按钮背景 - (维持) 透明，依赖边框 */
  --btn-secondary-hover: #00334D; /* 次要按钮悬停 - (调整) 填充您的次级背景色 */

  /* 链接颜色 (维持最佳实践) */
  --link-color: #00C4C5;
  --link-hover: #00D4FF;
  --link-visited: #008283;

  /* 特殊效果颜色 */
  --gradient-primary: linear-gradient(135deg, #005F73 0%, #00D4FF 100%); /* 渐变 - 起始色更深，对比更强烈 */
  --gradient-secondary: linear-gradient(180deg, #001B29 0%, #00111A 100%);/* 背景渐变 - 改为纵向，模拟深度 */
}