/* GamePort - Retro Theme (复古街机 - 橙黄色系) */
/* 80年代街机风格，经典橙黄配色 */

:root {
  /* 主要颜色变量 - 橙黄色系 */
  --primary-color: #f59e0b;
  --primary-color-2: #f59e0b;
  --primary-hover: #d97706;
  --primary-light: #fef3c7;
  --secondary-color: #92400e;
  --accent-color: #ff6b35;
  
  /* 背景颜色 */
  --bg-primary: #fffbeb;
  --bg-secondary: #fef3c7;
  --bg-tertiary: #fde68a;
  --bg-dark: #451a03;
  
  /* Header专用背景色 - 比主背景更深 */
  --header-bg-color: #fef3c7;
  
  /* 文字颜色 */
  --text-primary: #451a03;
  --text-secondary: #92400e;
  --text-light: #d97706;
  --text-white: #ffffff;
  
  /* 辅助颜色变量 */
  --border-color: #fde68a;
  --border-hover: #fcd34d;
  --shadow-color: rgba(245, 158, 11, 0.2);
  --shadow-hover: rgba(245, 158, 11, 0.3);
  
  /* 状态颜色 */
  --success-color: #65a30d;
  --warning-color: #f59e0b;
  --error-color: #dc2626;
  --info-color: #f59e0b;
  
  /* 按钮颜色状态 */
  --btn-primary-bg: #f59e0b;
  --btn-primary-hover: #d97706;
  --btn-primary-active: #b45309;
  --btn-secondary-bg: #92400e;
  --btn-secondary-hover: #78350f;
  
  /* 链接颜色 */
  --link-color: #f59e0b;
  --link-hover: #d97706;
  --link-visited: #92400e;
  
  /* 特殊效果颜色 */
  --gradient-primary: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --gradient-secondary: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}
